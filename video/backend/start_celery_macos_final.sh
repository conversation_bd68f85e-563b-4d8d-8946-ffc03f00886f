#!/bin/bash

# macOS 最终解决方案 - Celery Worker 启动脚本
# 专门解决 Apple Silicon (M1/M2/M4) 芯片上的 fork 问题

echo "🍎 macOS Celery Worker 最终解决方案"
echo "🔧 针对 Apple Silicon 芯片优化"
echo "============================================================"

# 1. 设置环境变量
echo "📝 设置环境变量..."
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
export PYTHONPATH=$(pwd)
echo "✅ 环境变量设置完成"

# 2. 检查 Redis 连接
echo "🔍 检查 Redis 连接..."
if redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis 连接正常"
else
    echo "❌ Redis 未运行，请先启动 Redis:"
    echo "   brew services start redis"
    echo "   或者: redis-server"
    exit 1
fi

# 3. 检查 Python 环境
echo "🐍 检查 Python 环境..."
if python -c "import celery" > /dev/null 2>&1; then
    echo "✅ Celery 已安装"
else
    echo "❌ Celery 未安装，请运行: pip install celery"
    exit 1
fi

# 4. 启动 Celery Worker
echo "🚀 启动 Celery Worker (solo 池 - 最稳定配置)..."
echo "⚠️  注意：使用 solo 池会限制并发性能，但确保稳定性"
echo "============================================================"

# 使用 exec 确保信号正确传递
exec celery -A app.tasks worker \
    --pool=solo \
    --concurrency=10 \
    --loglevel=info \
    --queues=celery,video_analysis,video_tasks,video \
    --hostname=macos-final-worker@%h \
    --without-gossip \
    --without-mingle \
    --time-limit=1800 \
    --soft-time-limit=1500
