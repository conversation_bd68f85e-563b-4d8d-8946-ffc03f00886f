#!/usr/bin/env python3
"""
简单的 Celery 测试，不依赖应用代码
"""

import os
from celery import Celery

# 设置环境变量避免 fork 问题
os.environ['OBJC_DISABLE_INITIALIZE_FORK_SAFETY'] = 'YES'

# 创建简单的 Celery 实例
app = Celery('simple_test', broker='redis://localhost:6379/1')

# 配置
app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
)

@app.task
def add(x, y):
    return x + y

@app.task
def hello():
    return "Hello from Celery!"

if __name__ == '__main__':
    print("🧪 启动简单 Celery 测试...")
    app.worker_main([
        'worker',
        '--loglevel=info',
        '--pool=gevent',
        '--concurrency=10'
    ])
