#!/usr/bin/env python3
"""
macOS 专用 Celery Worker 启动脚本
解决 Apple Silicon (M1/M2/M4) 芯片上的 fork 问题
"""

import sys
import os

# 设置环境变量，避免 fork 问题
os.environ['OBJC_DISABLE_INITIALIZE_FORK_SAFETY'] = 'YES'

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_environment():
    """设置环境"""
    print("🍎 macOS 环境设置...")
    
    # 设置必要的环境变量
    os.environ.setdefault('PYTHONPATH', os.path.dirname(os.path.abspath(__file__)))
    
    # 设置 multiprocessing 启动方法
    os.environ['CELERY_POOL'] = 'solo'
    
    print("✅ macOS 环境设置完成")

def import_all_tasks():
    """导入所有任务模块"""
    print("导入任务模块...")
    
    try:
        # 按顺序导入所有任务模块
        import app.tasks.video_tasks
        print("✅ video_tasks 导入成功")
        
        import app.tasks.video_tasks_basic_info
        print("✅ video_tasks_basic_info 导入成功")
        
        import app.tasks.video_tasks_content
        print("✅ video_tasks_content 导入成功")
        
        import app.tasks.video_tasks_content_roles
        print("✅ video_tasks_content_roles 导入成功")
        
        import app.tasks.video_tasks_plot
        print("✅ video_tasks_plot 导入成功")
        
        import app.tasks.video_tasks_minicpm
        print("✅ video_tasks_minicpm 导入成功")
        
        # 导入任务包以确保所有任务被注册
        import app.tasks
        print("✅ app.tasks 包导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_celery_worker():
    """启动 Celery Worker (macOS 优化版)"""
    print("启动 Celery Worker (macOS 优化版)...")
    
    try:
        from app.tasks.celery import celery
        
        # macOS 专用启动参数
        celery.worker_main([
            'worker',
            '--loglevel=info',
            '--pool=solo',  # 使用 solo 池避免 fork 问题
            '--concurrency=1',  # solo 池只支持单进程
            '--queues=celery,video_analysis',
            '--hostname=macos-worker@%h',
            '--without-gossip',  # 禁用 gossip 协议
            '--without-mingle',  # 禁用 mingle
            '--without-heartbeat',  # 禁用心跳
        ])
        
    except KeyboardInterrupt:
        print("\n👋 Celery Worker 已停止")
    except Exception as e:
        print(f"❌ Celery Worker 启动失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 启动 macOS 优化版 Celery Worker...")
    print("🍎 专为 Apple Silicon (M1/M2/M4) 芯片优化")
    print("=" * 60)
    
    # 1. 设置 macOS 环境
    setup_environment()
    print()
    
    # 2. 导入所有任务模块
    if not import_all_tasks():
        print("❌ 任务模块导入失败，退出")
        sys.exit(1)
    print()
    
    # 3. 启动 Celery Worker
    print("🔄 启动 Worker (使用 solo 池，避免 fork 问题)...")
    start_celery_worker()

if __name__ == "__main__":
    main()
