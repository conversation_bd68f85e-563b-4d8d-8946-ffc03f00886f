#!/usr/bin/env python3
"""
测试人脸检测任务的脚本
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.tasks.video_tasks_content_roles_simple import test_tasks

def main():
    """主函数"""
    print("=" * 50)
    print("人脸检测任务测试")
    print("=" * 50)
    
    # 测试任务注册
    success = test_tasks()
    
    if success:
        print("\n✅ 所有任务注册成功！")
        print("\n可以使用以下任务：")
        print("1. analyze_video_face_detection - 视频人脸检测")
        print("2. analyze_task_character_clustering - 人物角色聚类")
        print("3. generate_character_appearances - 生成人物出现记录")
    else:
        print("\n❌ 任务注册失败，请检查配置")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
