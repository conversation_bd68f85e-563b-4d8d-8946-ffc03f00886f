#!/usr/bin/env python3
"""
macOS Celery Fork 问题修复脚本
专门解决 Apple Silicon 芯片上的 Swift 运行时冲突问题
"""

import os
import sys
import subprocess

def apply_macos_fixes():
    """应用 macOS 特定的修复"""
    print("🍎 应用 macOS 特定修复...")
    
    # 1. 设置环境变量
    os.environ['OBJC_DISABLE_INITIALIZE_FORK_SAFETY'] = 'YES'
    os.environ['CELERY_POOL'] = 'threads'
    
    # 2. 禁用一些可能导致问题的功能
    os.environ['CELERY_DISABLE_RATE_LIMITS'] = '1'
    
    print("✅ 环境变量已设置")

def start_celery_with_fixes():
    """使用修复启动 Celery"""
    print("🚀 启动 Celery Worker (macOS 修复版)...")
    
    # 应用修复
    apply_macos_fixes()
    
    # 构建命令
    cmd = [
        'celery',
        '-A', 'app.tasks',
        'worker',
        '--pool=threads',
        '--concurrency=2',
        '--loglevel=info',
        '--queues=celery,video_analysis',
        '--hostname=macos-fixed-worker@%h',
        '--without-gossip',
        '--without-mingle'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 使用 subprocess 启动，避免 Python 内部的 fork 问题
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 Worker 已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ Worker 启动失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🔧 macOS Celery 修复工具")
    print("=" * 50)
    
    if not start_celery_with_fixes():
        sys.exit(1)
