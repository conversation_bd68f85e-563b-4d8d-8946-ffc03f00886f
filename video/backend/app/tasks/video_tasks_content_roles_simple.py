"""
简化版人物角色分析任务
用于测试任务注册，避免复杂的依赖问题
"""

import os
import cv2
import numpy as np
import time
from typing import List, Dict, Any
from loguru import logger
from sqlalchemy.orm import Session

from celery import current_task
from app.tasks.celery import celery
from app.core.database import SessionLocal
from app.models.task import Video, VideoFrame, FaceDetection
from app.services.insightface_service import insightface_service
from app.core.config import settings
from .task_logger import TaskLogger


@celery.task(bind=True)
def analyze_video_face_detection(self, video_id: int):
    """
    视频人脸检测任务
    """
    task_logger = TaskLogger("FACE_DETECTION", video_id=video_id)
    task_logger.start_task(description=f"视频 {video_id} 人脸检测分析")
    
    db = SessionLocal()
    start_time = time.time()
    
    try:
        # 步骤1: 获取视频信息
        task_logger.start_step("获取视频信息")
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise Exception(f"Video {video_id} not found")
        task_logger.complete_step("获取视频信息", f"视频文件: {video.filename}")
        
        # 步骤2: 初始化InsightFace服务
        task_logger.start_step("初始化人脸识别服务")
        if not insightface_service.initialized:
            try:
                success = insightface_service.initialize_sync()
                if not success:
                    raise Exception("Failed to initialize InsightFace service")
            except Exception as e:
                task_logger.log_error("InsightFace服务初始化失败", e)
                task_logger.complete_step("初始化人脸识别服务", f"跳过人脸检测: {str(e)}")
                return {
                    'status': 'skipped',
                    'video_id': video_id,
                    'faces_detected': 0,
                    'message': 'InsightFace service not available'
                }
        task_logger.complete_step("初始化人脸识别服务", "InsightFace服务已就绪")

        # 步骤3: 获取视频帧
        task_logger.start_step("获取视频帧")
        frames = db.query(VideoFrame).filter(VideoFrame.video_id == video_id).all()
        if not frames:
            raise Exception(f"No frames found for video {video_id}")
        task_logger.complete_step("获取视频帧", f"找到 {len(frames)} 个视频帧")

        # 步骤4: 人脸检测处理
        task_logger.start_step("人脸检测处理")
        face_detections = []
        processed_frames = 0
        total_faces_detected = 0

        # 创建人脸图片存储目录
        face_storage_dir = os.path.join(
            getattr(settings, 'VIDEOS_STORAGE_DIR', '../storage/videos'),
            'faces',
            str(video.task_id),
            str(video_id)
        )
        os.makedirs(face_storage_dir, exist_ok=True)

        for i, frame in enumerate(frames):
            try:
                # 读取帧图片
                if not os.path.exists(frame.file_path):
                    logger.warning(f"Frame file not found: {frame.file_path}")
                    continue

                image = cv2.imread(frame.file_path)
                if image is None:
                    logger.warning(f"Failed to load frame: {frame.file_path}")
                    continue

                # 检测人脸
                faces = insightface_service.detect_faces(image)

                # 处理检测到的人脸
                for j, face in enumerate(faces):
                    # 保存人脸图片
                    face_filename = f"face_{frame.id}_{j}.jpg"
                    face_path = os.path.join(face_storage_dir, face_filename)

                    # 增强人脸图像质量
                    enhanced_face = insightface_service.enhance_face_image(face['face_image'])

                    # 保存人脸图片
                    if insightface_service.save_face_image(enhanced_face, face_path):
                        # 创建人脸检测记录
                        face_detection = FaceDetection(
                            video_id=video_id,
                            frame_id=frame.id,
                            timestamp=frame.timestamp,
                            bbox=face['bbox'],
                            embedding_vector=face['embedding'].tobytes(),
                            confidence=face['confidence'],
                            face_image_path=face_path
                        )

                        face_detections.append(face_detection)
                        total_faces_detected += 1

                processed_frames += 1

                # 更新进度
                progress = (processed_frames / len(frames)) * 100
                current_task.update_state(
                    state='PROGRESS',
                    meta={
                        'current': processed_frames,
                        'total': len(frames),
                        'status': f'已处理 {processed_frames}/{len(frames)} 帧，检测到 {total_faces_detected} 个人脸'
                    }
                )

                if processed_frames % 10 == 0:
                    task_logger.log_progress(
                        processed_frames, len(frames),
                        f"人脸检测进度: {total_faces_detected} 个人脸"
                    )

            except Exception as e:
                logger.error(f"Failed to process frame {frame.id}: {e}")
                continue

        task_logger.complete_step("人脸检测处理", f"处理完成，检测到 {total_faces_detected} 个人脸")

        # 步骤5: 保存检测结果
        task_logger.start_step("保存检测结果")
        if face_detections:
            db.add_all(face_detections)
            db.commit()
            task_logger.complete_step("保存检测结果", f"保存了 {len(face_detections)} 条人脸检测记录")
        else:
            task_logger.complete_step("保存检测结果", "未检测到人脸，无需保存")

        faces_detected = total_faces_detected
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        task_logger.complete_task(
            True,
            f"人脸检测完成，总耗时: {processing_time:.2f}s，检测到 {faces_detected} 个人脸"
        )

        return {
            'status': 'completed',
            'video_id': video_id,
            'faces_detected': faces_detected,
            'processing_time': processing_time
        }
        
    except Exception as e:
        task_logger.log_error("人脸检测过程中发生异常", e)
        task_logger.complete_task(False, f"人脸检测失败: {str(e)}")
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def analyze_task_character_clustering(self, task_id: int):
    """
    人物角色聚类任务（简化版，仅统计人脸检测结果）
    """
    task_logger = TaskLogger("CHARACTER_CLUSTERING", task_id=task_id)
    task_logger.start_task(description=f"任务 {task_id} 人物角色聚类分析")

    db = SessionLocal()
    start_time = time.time()

    try:
        # 步骤1: 获取任务信息
        task_logger.start_step("获取任务信息")
        from app.models.task import Task
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise Exception(f"Task {task_id} not found")
        task_logger.complete_step("获取任务信息", f"任务名称: {task.name}")

        # 步骤2: 收集人脸检测数据
        task_logger.start_step("收集人脸检测数据")
        face_detections = db.query(FaceDetection).join(Video).filter(
            Video.task_id == task_id
        ).all()

        if not face_detections:
            task_logger.complete_step("收集人脸检测数据", "未找到人脸检测数据")
            task_logger.complete_task(True, "无人脸数据，跳过聚类分析")
            return {'status': 'completed', 'message': 'No face data found'}

        task_logger.complete_step("收集人脸检测数据", f"收集到 {len(face_detections)} 个人脸数据")

        # 步骤3: 统计分析（简化版，不进行实际聚类）
        task_logger.start_step("统计分析")

        # 按视频统计人脸数量
        video_face_counts = {}
        for detection in face_detections:
            video_id = detection.video_id
            if video_id not in video_face_counts:
                video_face_counts[video_id] = 0
            video_face_counts[video_id] += 1

        # 估算人物角色数量（简化算法）
        total_faces = len(face_detections)
        estimated_characters = min(max(total_faces // 10, 1), 20)  # 每10个人脸估算1个角色，最少1个，最多20个

        task_logger.complete_step("统计分析", f"估算识别出 {estimated_characters} 个人物角色")

        # 计算处理时间
        processing_time = time.time() - start_time

        task_logger.complete_task(
            True,
            f"人物角色聚类分析完成，总耗时: {processing_time:.2f}s，估算 {estimated_characters} 个角色"
        )

        return {
            'status': 'completed',
            'task_id': task_id,
            'characters_estimated': estimated_characters,
            'total_faces': total_faces,
            'video_count': len(video_face_counts),
            'processing_time': processing_time
        }
        
    except Exception as e:
        task_logger.log_error("人物角色聚类过程中发生异常", e)
        task_logger.complete_task(False, f"人物角色聚类失败: {str(e)}")
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def generate_character_appearances(self, task_id: int):
    """
    生成人物出现记录任务（简化版，基于人脸检测数据统计）
    """
    task_logger = TaskLogger("CHARACTER_APPEARANCES", task_id=task_id)
    task_logger.start_task(description=f"任务 {task_id} 生成人物出现记录")

    db = SessionLocal()

    try:
        # 获取任务信息
        from app.models.task import Task
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise Exception(f"Task {task_id} not found")

        # 获取人脸检测数据
        face_detections = db.query(FaceDetection).join(Video).filter(
            Video.task_id == task_id
        ).all()

        if not face_detections:
            task_logger.complete_task(True, "无人脸检测数据")
            return {'status': 'completed', 'message': 'No face detection data found'}

        # 按视频和时间统计人脸出现
        video_appearances = {}
        for detection in face_detections:
            video_id = detection.video_id
            if video_id not in video_appearances:
                video_appearances[video_id] = []
            video_appearances[video_id].append({
                'timestamp': detection.timestamp,
                'confidence': detection.confidence,
                'bbox': detection.bbox
            })

        # 统计总的出现记录数
        total_appearances = sum(len(appearances) for appearances in video_appearances.values())

        task_logger.complete_task(
            True,
            f"人物出现记录统计完成，发现 {total_appearances} 个人脸出现记录"
        )

        return {
            'status': 'completed',
            'task_id': task_id,
            'appearances_found': total_appearances,
            'videos_with_faces': len(video_appearances)
        }
        
    except Exception as e:
        task_logger.log_error("生成人物出现记录过程中发生异常", e)
        task_logger.complete_task(False, f"生成失败: {str(e)}")
        raise e
    finally:
        db.close()




