"""
InsightFace人脸识别服务
用于人脸检测、特征提取和识别
"""

import cv2
import numpy as np
import os
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
import insightface
from insightface.app import FaceAnalysis
from app.core.config import settings


class InsightFaceService:
    """InsightFace人脸识别服务类"""
    
    def __init__(self):
        self.model_name = getattr(settings, 'INSIGHTFACE_MODEL', 'buffalo_l')
        self.device = getattr(settings, 'INSIGHTFACE_DEVICE', 'cuda')
        self.det_size = getattr(settings, 'FACE_DETECTION_SIZE', (640, 640))
        self.min_face_size = getattr(settings, 'MIN_FACE_SIZE', 30)
        self.confidence_threshold = getattr(settings, 'FACE_CONFIDENCE_THRESHOLD', 0.5)
        
        self.app = None
        self.initialized = False
    
    async def initialize(self) -> bool:
        """异步初始化InsightFace模型"""
        return self.initialize_sync()

    def initialize_sync(self) -> bool:
        """同步初始化InsightFace模型"""
        try:
            # 创建FaceAnalysis应用
            self.app = FaceAnalysis(
                name=self.model_name,
                providers=['CoreMLExecutionProvider', 'CPUExecutionProvider'] # if self.device == 'cuda' else ['CPUExecutionProvider']
            )

            # 准备模型
            self.app.prepare(ctx_id=0 if self.device == 'cuda' else -1, det_size=self.det_size)

            self.initialized = True
            logger.info(f"InsightFace model {self.model_name} initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize InsightFace: {e}")
            return False
    
    def detect_faces(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        检测图像中的人脸
        
        Args:
            image: 输入图像（BGR格式）
        
        Returns:
            人脸检测结果列表，每个元素包含：
            - bbox: 边界框 [x, y, width, height]
            - confidence: 置信度
            - embedding: 特征向量
            - landmarks: 关键点
            - face_image: 裁切的人脸图像
        """
        if not self.initialized:
            raise RuntimeError("InsightFace not initialized")
        
        try:
            # 检测人脸
            faces = self.app.get(image)
            
            results = []
            for face in faces:
                # 检查置信度
                if face.det_score < self.confidence_threshold:
                    continue
                
                # 获取边界框
                bbox = face.bbox.astype(int)
                x, y, x2, y2 = bbox
                width = x2 - x
                height = y2 - y
                
                # 检查人脸大小
                if width < self.min_face_size or height < self.min_face_size:
                    continue
                
                # 裁切人脸图像
                face_image = image[y:y2, x:x2]
                
                # 获取特征向量
                embedding = face.normed_embedding
                
                # 获取关键点
                landmarks = face.kps if hasattr(face, 'kps') else None
                
                results.append({
                    'bbox': [x, y, width, height],
                    'confidence': float(face.det_score),
                    'embedding': embedding,
                    'landmarks': landmarks,
                    'face_image': face_image
                })
            
            logger.debug(f"Detected {len(results)} faces in image")
            return results
            
        except Exception as e:
            logger.error(f"Failed to detect faces: {e}")
            return []
    
    def extract_face_embedding(self, face_image: np.ndarray) -> Optional[np.ndarray]:
        """
        从人脸图像提取特征向量
        
        Args:
            face_image: 人脸图像
        
        Returns:
            特征向量，如果提取失败返回None
        """
        if not self.initialized:
            raise RuntimeError("InsightFace not initialized")
        
        try:
            faces = self.app.get(face_image)
            if len(faces) > 0:
                return faces[0].normed_embedding
            return None
            
        except Exception as e:
            logger.error(f"Failed to extract face embedding: {e}")
            return None
    
    def compare_faces(
        self, 
        embedding1: np.ndarray, 
        embedding2: np.ndarray
    ) -> float:
        """
        比较两个人脸特征向量的相似度
        
        Args:
            embedding1: 第一个特征向量
            embedding2: 第二个特征向量
        
        Returns:
            相似度分数（0-1之间，越高越相似）
        """
        try:
            # 计算余弦相似度
            similarity = np.dot(embedding1, embedding2) / (
                np.linalg.norm(embedding1) * np.linalg.norm(embedding2)
            )
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Failed to compare faces: {e}")
            return 0.0
    
    def save_face_image(
        self, 
        face_image: np.ndarray, 
        save_path: str,
        quality: int = 95
    ) -> bool:
        """
        保存人脸图像
        
        Args:
            face_image: 人脸图像
            save_path: 保存路径
            quality: JPEG质量（1-100）
        
        Returns:
            是否保存成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 保存图像
            cv2.imwrite(save_path, face_image, [cv2.IMWRITE_JPEG_QUALITY, quality])
            return True
            
        except Exception as e:
            logger.error(f"Failed to save face image: {e}")
            return False
    
    def enhance_face_image(self, face_image: np.ndarray) -> np.ndarray:
        """
        增强人脸图像质量
        
        Args:
            face_image: 原始人脸图像
        
        Returns:
            增强后的人脸图像
        """
        try:
            # 调整大小到标准尺寸
            target_size = (112, 112)  # InsightFace标准尺寸
            enhanced = cv2.resize(face_image, target_size, interpolation=cv2.INTER_CUBIC)
            
            # 直方图均衡化
            if len(enhanced.shape) == 3:
                # 彩色图像
                enhanced = cv2.cvtColor(enhanced, cv2.COLOR_BGR2YUV)
                enhanced[:, :, 0] = cv2.equalizeHist(enhanced[:, :, 0])
                enhanced = cv2.cvtColor(enhanced, cv2.COLOR_YUV2BGR)
            else:
                # 灰度图像
                enhanced = cv2.equalizeHist(enhanced)
            
            # 高斯滤波去噪
            enhanced = cv2.GaussianBlur(enhanced, (3, 3), 0)
            
            return enhanced
            
        except Exception as e:
            logger.error(f"Failed to enhance face image: {e}")
            return face_image
    
    def calculate_face_quality(self, face_image: np.ndarray) -> float:
        """
        计算人脸图像质量分数
        
        Args:
            face_image: 人脸图像
        
        Returns:
            质量分数（0-1之间，越高质量越好）
        """
        try:
            # 计算图像清晰度（拉普拉斯方差）
            gray = cv2.cvtColor(face_image, cv2.COLOR_BGR2GRAY) if len(face_image.shape) == 3 else face_image
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            # 计算亮度
            brightness = np.mean(gray)
            
            # 计算对比度
            contrast = np.std(gray)
            
            # 综合质量分数
            # 清晰度权重0.5，亮度权重0.3，对比度权重0.2
            sharpness_score = min(laplacian_var / 1000.0, 1.0)  # 归一化到0-1
            brightness_score = 1.0 - abs(brightness - 128) / 128.0  # 理想亮度128
            contrast_score = min(contrast / 64.0, 1.0)  # 归一化到0-1
            
            quality_score = (
                sharpness_score * 0.5 + 
                brightness_score * 0.3 + 
                contrast_score * 0.2
            )
            
            return float(quality_score)
            
        except Exception as e:
            logger.error(f"Failed to calculate face quality: {e}")
            return 0.0
    
    def batch_detect_faces(
        self, 
        images: List[np.ndarray]
    ) -> List[List[Dict[str, Any]]]:
        """
        批量检测多张图像中的人脸
        
        Args:
            images: 图像列表
        
        Returns:
            每张图像的人脸检测结果列表
        """
        results = []
        for i, image in enumerate(images):
            try:
                faces = self.detect_faces(image)
                results.append(faces)
                logger.debug(f"Processed image {i+1}/{len(images)}, found {len(faces)} faces")
            except Exception as e:
                logger.error(f"Failed to process image {i+1}: {e}")
                results.append([])
        
        return results
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "device": self.device,
            "det_size": self.det_size,
            "min_face_size": self.min_face_size,
            "confidence_threshold": self.confidence_threshold,
            "initialized": self.initialized
        }


# 全局InsightFace服务实例
insightface_service = InsightFaceService()
