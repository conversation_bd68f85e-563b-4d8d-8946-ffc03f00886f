#!/bin/bash

# macOS 专用 Celery Worker 启动脚本
# 解决 Apple Silicon (M1/M2/M4) 芯片上的 fork 问题

echo "🍎 启动 macOS 优化版 Celery Worker..."
echo "🔧 设置环境变量以避免 fork 问题..."

# 设置环境变量避免 fork 问题
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES

# 启动 Celery Worker 使用 threads 池
echo "🚀 启动 Worker (使用 threads 池)..."
celery -A app.tasks worker \
    --pool=threads \
    --concurrency=4 \
    --loglevel=info \
    --queues=celery,video_analysis \
    --hostname=macos-worker@%h
