# macOS Celery Fork 问题解决方案

## 问题描述

在 macOS 上，特别是 Apple Silicon (M1/M2/M4) 芯片的 Mac 上，运行 Celery 时会遇到以下错误：

```
objc[46503]: +[Swift.__SharedStringStorage initialize] may have been in progress in another thread when fork() was called.
objc[46503]: +[Swift.__SharedStringStorage initialize] may have been in progress in another thread when fork() was called. We cannot safely call it or ignore it in the fork() child process. Crashing instead.
[2025-08-10 10:24:42,179: ERROR/MainProcess] Process 'ForkPoolWorker-9' pid:46503 exited with 'signal 6 (SIGABRT)'
```

## 根本原因

这个问题是由于 macOS 系统中的 Swift 运行时组件与 Python 的 `fork()` 系统调用冲突导致的。

## 解决方案

### 1. 环境变量设置

在启动 Celery 之前设置以下环境变量：

```bash
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
```

### 2. 修改 Celery 配置

在 `app/tasks/celery.py` 中，使用以下配置：

```python
# Celery配置
celery.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30分钟超时
    task_soft_time_limit=25 * 60,  # 25分钟软超时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    broker_connection_retry_on_startup = True,
    # 修复 macOS 上的 fork 问题 - 使用 solo 池 (最稳定)
    worker_pool='solo',  # 使用 solo 池避免 fork 问题
    worker_concurrency=1,  # solo 池只支持单进程
)
```

### 3. 启动命令

使用以下命令启动 Celery Worker：

```bash
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
celery -A app.tasks worker \
    --pool=solo \
    --concurrency=1 \
    --loglevel=info \
    --queues=celery,video_analysis \
    --hostname=macos-worker@%h
```

### 4. 推荐的启动脚本

创建 `start_celery_macos.sh`：

```bash
#!/bin/bash

# macOS 专用 Celery Worker 启动脚本
echo "🍎 启动 macOS 优化版 Celery Worker..."

# 设置环境变量避免 fork 问题
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES

# 检查 Redis 连接
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis 未运行，请先启动 Redis"
    exit 1
fi

# 启动 Celery Worker
celery -A app.tasks worker \
    --pool=solo \
    --concurrency=1 \
    --loglevel=info \
    --queues=celery,video_analysis \
    --hostname=macos-worker@%h
```

### 5. 替代方案

如果 `solo` 池性能不够，可以尝试以下替代方案：

#### 使用 threads 池：
```bash
celery -A app.tasks worker --pool=threads --concurrency=4
```

#### 使用 eventlet 池（需要安装 eventlet）：
```bash
pip install eventlet
celery -A app.tasks worker --pool=eventlet --concurrency=100
```

#### 使用 gevent 池（需要安装 gevent）：
```bash
pip install gevent
celery -A app.tasks worker --pool=gevent --concurrency=100
```

## 性能考虑

- `solo` 池：最稳定，但只支持单进程，性能较低
- `threads` 池：支持多线程，适合 I/O 密集型任务
- `eventlet/gevent` 池：支持高并发，适合网络密集型任务

## 验证解决方案

启动 Worker 后，应该看到类似以下输出：

```
[2025-08-10 10:30:00,000: INFO/MainProcess] Connected to redis://localhost:6379/1
[2025-08-10 10:30:00,000: INFO/MainProcess] mingle: searching for neighbors
[2025-08-10 10:30:00,000: INFO/MainProcess] mingle: all alone
[2025-08-10 10:30:00,000: INFO/MainProcess] celery@macos-worker ready.
```

如果没有看到 `signal 6 (SIGABRT)` 错误，说明问题已解决。

## 故障排除

如果问题仍然存在：

1. 确保 Redis 正在运行：`redis-cli ping`
2. 检查 Python 环境是否正确
3. 尝试重新安装 Celery：`pip install --upgrade celery`
4. 考虑使用 Docker 容器来隔离环境

## 注意事项

- 这个修复方案会降低并发性能，但确保了稳定性
- 对于生产环境，建议使用 Docker 或 Linux 服务器
- 定期更新 macOS 和相关依赖可能会改善这个问题
