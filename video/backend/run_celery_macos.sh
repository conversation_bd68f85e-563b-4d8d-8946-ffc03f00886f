#!/bin/bash

# macOS 终极解决方案
# 解决 Apple Silicon 芯片上的 Celery fork 问题

echo "🍎 macOS Celery 终极解决方案"
echo "🔧 针对 Apple Silicon (M1/M2/M4) 芯片优化"
echo "============================================================"

# 1. 设置所有可能的环境变量
echo "设置环境变量..."
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
export CELERY_POOL=solo
export CELERY_CONCURRENCY=1
export PYTHONPATH=$(pwd)

# 2. 检查 Redis 连接
echo "检查 Redis 连接..."
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis 未运行，请先启动 Redis"
    echo "可以运行: brew services start redis"
    exit 1
fi
echo "✅ Redis 连接正常"

# 3. 使用最兼容的配置启动 Celery
echo "🚀 启动 Celery Worker (最兼容配置)..."
exec celery -A app.tasks worker \
    --pool=solo \
    --concurrency=1 \
    --loglevel=info \
    --queues=celery,video_analysis \
    --hostname=macos-solo-worker@%h \
    --without-gossip \
    --without-mingle \
    --without-heartbeat \
    --time-limit=300 \
    --soft-time-limit=240
