#!/usr/bin/env python3
"""
最终的 Celery Worker 启动脚本
专门解决 macOS 上的 fork 问题
"""

import os
import sys

def main():
    # 设置环境变量
    os.environ['OBJC_DISABLE_INITIALIZE_FORK_SAFETY'] = 'YES'
    
    print("🍎 macOS Celery Worker 启动器")
    print("🔧 设置环境变量以避免 fork 问题...")
    
    try:
        # 导入 Celery 应用
        from app.tasks.celery import celery
        print("✅ Celery 应用导入成功")
        
        # 启动 worker
        print("🚀 启动 Worker...")
        celery.worker_main([
            'worker',
            '--pool=solo',
            '--concurrency=1',
            '--loglevel=info',
            '--queues=celery,video_analysis',
            '--hostname=macos-final-worker@%h'
        ])
        
    except KeyboardInterrupt:
        print("\n👋 Worker 已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
