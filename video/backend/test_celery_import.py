#!/usr/bin/env python3
"""
测试 Celery 导入和配置
"""

import sys
import os

# 设置环境变量避免 fork 问题
os.environ['OBJC_DISABLE_INITIALIZE_FORK_SAFETY'] = 'YES'

print("🔍 测试 Celery 导入和配置...")
print("Python version:", sys.version)
print("Current directory:", os.getcwd())

try:
    print("\n1. 导入 app 模块...")
    import app
    print("✅ app module imported")
    
    print("\n2. 导入 app.tasks...")
    import app.tasks
    print("✅ app.tasks imported")
    
    print("\n3. 导入 app.tasks.celery...")
    import app.tasks.celery
    print("✅ app.tasks.celery imported")
    
    print("\n4. 获取 celery 实例...")
    from app.tasks.celery import celery
    print("✅ celery instance imported")
    
    print("\n5. 检查注册的任务...")
    print(f"Registered tasks: {len(celery.tasks)}")
    
    task_list = list(celery.tasks.keys())
    print("前10个任务:")
    for i, task in enumerate(task_list[:10]):
        print(f"  {i+1}. {task}")
    
    print("\n6. 检查人脸识别相关任务...")
    face_tasks = [task for task in task_list if 'face' in task.lower() or 'character' in task.lower()]
    print(f"人脸/人物相关任务 ({len(face_tasks)} 个):")
    for task in face_tasks:
        print(f"  - {task}")
    
    print("\n✅ 所有导入测试通过!")
    
except Exception as e:
    print(f"\n❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n🚀 尝试启动 Celery Worker...")
try:
    # 尝试启动 worker
    celery.worker_main([
        'worker',
        '--loglevel=info',
        '--pool=threads',
        '--concurrency=2',
        '--queues=celery,video_analysis',
        '--hostname=test-worker@%h'
    ])
except KeyboardInterrupt:
    print("\n👋 Worker 已停止")
except Exception as e:
    print(f"\n❌ Worker 启动失败: {e}")
    import traceback
    traceback.print_exc()
